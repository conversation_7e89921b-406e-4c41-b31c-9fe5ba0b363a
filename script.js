class DeepSeekChat {
    constructor() {
        this.messages = [];
        this.isTyping = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupTextareaAutoResize();
    }

    bindEvents() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('open');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            }
        });

        // New chat button
        const newChatBtn = document.getElementById('newChatBtn');
        newChatBtn.addEventListener('click', () => {
            this.startNewChat();
        });

        // Send message
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        
        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Suggestion cards
        const suggestionCards = document.querySelectorAll('.suggestion-card');
        suggestionCards.forEach(card => {
            card.addEventListener('click', () => {
                const text = this.getSuggestionText(card);
                messageInput.value = text;
                messageInput.focus();
            });
        });

        // Chat history items
        const chatItems = document.querySelectorAll('.chat-item');
        chatItems.forEach(item => {
            item.addEventListener('click', () => {
                this.selectChatItem(item);
            });
        });

        // User menu
        this.setupUserMenu();

        // Settings modal
        this.setupSettingsModal();

        // Sidebar collapse
        this.setupSidebarCollapse();

        // Feature buttons
        this.setupFeatureButtons();
    }

    setupTextareaAutoResize() {
        const textarea = document.getElementById('messageInput');

        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';

            // Update send button state
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = !textarea.value.trim();
        });
    }

    setupUserMenu() {
        const userInfo = document.getElementById('userInfo');
        const userMenuOverlay = document.getElementById('userMenuOverlay');
        const userMenu = document.getElementById('userMenu');

        // Show user menu
        userInfo.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showUserMenu();
        });

        // Hide user menu when clicking overlay
        userMenuOverlay.addEventListener('click', (e) => {
            if (e.target === userMenuOverlay) {
                this.hideUserMenu();
            }
        });

        // Prevent menu from closing when clicking inside
        userMenu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Menu item handlers
        document.getElementById('systemSettings').addEventListener('click', () => {
            this.handleSystemSettings();
        });

        document.getElementById('contactUs').addEventListener('click', () => {
            this.handleContactUs();
        });

        document.getElementById('logout').addEventListener('click', () => {
            this.handleLogout();
        });

        document.getElementById('personalInfo').addEventListener('click', () => {
            this.handlePersonalInfo();
        });

        // Close menu with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideUserMenu();
            }
        });
    }

    showUserMenu() {
        const userMenuOverlay = document.getElementById('userMenuOverlay');
        userMenuOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    hideUserMenu() {
        const userMenuOverlay = document.getElementById('userMenuOverlay');
        userMenuOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }

    handleSystemSettings() {
        this.hideUserMenu();
        this.showSettingsModal();
    }

    handleContactUs() {
        this.hideUserMenu();
        this.showNotification('联系我们：<EMAIL>', 'info');
    }

    handleLogout() {
        this.hideUserMenu();
        if (confirm('确定要退出登录吗？')) {
            this.showNotification('已退出登录', 'success');
            // In a real app, this would redirect to login page
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }
    }

    handlePersonalInfo() {
        this.hideUserMenu();
        this.showNotification('个人信息页面开发中...', 'info');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px',
            zIndex: '3000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });

        // Set background color based on type
        const colors = {
            info: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    setupSettingsModal() {
        const settingsModalOverlay = document.getElementById('settingsModalOverlay');
        const settingsModal = document.getElementById('settingsModal');
        const closeSettingsModal = document.getElementById('closeSettingsModal');

        // Close modal handlers
        closeSettingsModal.addEventListener('click', () => {
            this.hideSettingsModal();
        });

        settingsModalOverlay.addEventListener('click', (e) => {
            if (e.target === settingsModalOverlay) {
                this.hideSettingsModal();
            }
        });

        // Prevent modal from closing when clicking inside
        settingsModal.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Tab switching
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.getAttribute('data-tab');

                // Remove active class from all tabs and panels
                tabBtns.forEach(b => b.classList.remove('active'));
                tabPanels.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab and corresponding panel
                btn.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Settings controls
        this.setupSettingsControls();

        // Close with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideSettingsModal();
            }
        });
    }

    setupSettingsControls() {
        // Language selector
        const languageSelect = document.getElementById('languageSelect');
        languageSelect.addEventListener('change', (e) => {
            this.showNotification(`语言已切换到: ${e.target.selectedOptions[0].text}`, 'success');
        });

        // Theme selector
        const themeSelect = document.getElementById('themeSelect');
        themeSelect.addEventListener('change', (e) => {
            const theme = e.target.value;
            this.applyTheme(theme);
            this.showNotification(`主题已切换到: ${e.target.selectedOptions[0].text}`, 'success');
        });

        // Font size selector
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        fontSizeSelect.addEventListener('change', (e) => {
            const fontSize = e.target.value;
            this.applyFontSize(fontSize);
            this.showNotification(`字体大小已调整为: ${e.target.selectedOptions[0].text}`, 'success');
        });

        // Send key selector
        const sendKeySelect = document.getElementById('sendKeySelect');
        sendKeySelect.addEventListener('change', (e) => {
            this.showNotification(`发送快捷键已设置为: ${e.target.selectedOptions[0].text}`, 'success');
        });

        // Auto save toggle
        const autoSaveToggle = document.getElementById('autoSaveToggle');
        autoSaveToggle.addEventListener('change', (e) => {
            const status = e.target.checked ? '开启' : '关闭';
            this.showNotification(`自动保存对话已${status}`, 'success');
        });

        // Typing effect toggle
        const typingEffectToggle = document.getElementById('typingEffectToggle');
        typingEffectToggle.addEventListener('change', (e) => {
            const status = e.target.checked ? '开启' : '关闭';
            this.showNotification(`打字效果已${status}`, 'success');
        });

        // Account action buttons
        const actionBtns = document.querySelectorAll('.account-actions .action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.textContent;
                if (action === '删除账号') {
                    if (confirm('确定要删除账号吗？此操作不可恢复！')) {
                        this.showNotification('账号删除功能暂未开放', 'warning');
                    }
                } else {
                    this.showNotification(`${action}功能开发中...`, 'info');
                }
            });
        });

        // Terms action buttons
        const termsActionBtns = document.querySelectorAll('.terms-actions .action-btn');
        termsActionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.textContent;
                this.showNotification(`${action}功能开发中...`, 'info');
            });
        });
    }

    showSettingsModal() {
        const settingsModalOverlay = document.getElementById('settingsModalOverlay');
        settingsModalOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    hideSettingsModal() {
        const settingsModalOverlay = document.getElementById('settingsModalOverlay');
        settingsModalOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }

    applyTheme(theme) {
        const body = document.body;
        body.classList.remove('light-theme', 'dark-theme');

        if (theme === 'dark') {
            body.classList.add('dark-theme');
        } else if (theme === 'light') {
            body.classList.add('light-theme');
        }
        // 'auto' theme follows system preference
    }

    applyFontSize(size) {
        const body = document.body;
        body.classList.remove('font-small', 'font-medium', 'font-large');
        body.classList.add(`font-${size}`);
    }

    setupSidebarCollapse() {
        const collapseBtn = document.getElementById('collapseBtn');
        const sidebar = document.getElementById('sidebar');

        collapseBtn.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');

            // Update button title and tooltip
            const isCollapsed = sidebar.classList.contains('collapsed');
            collapseBtn.title = isCollapsed ? '展开侧边栏' : '收缩侧边栏';

            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        });

        // Restore saved state
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            sidebar.classList.add('collapsed');
            collapseBtn.title = '展开侧边栏';
        }

        // Handle collapsed icons
        this.setupCollapsedIcons();
    }

    setupCollapsedIcons() {
        const collapsedIcons = document.querySelectorAll('.collapsed-icon');
        collapsedIcons.forEach(icon => {
            icon.addEventListener('click', () => {
                const title = icon.getAttribute('title');
                this.showNotification(`${title}功能开发中...`, 'info');
            });
        });
    }

    setupFeatureButtons() {
        const featureButtons = document.querySelectorAll('.feature-btn');

        featureButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // Toggle active state for clicked button
                const isActive = btn.classList.contains('active');

                if (isActive) {
                    // If already active, deactivate it
                    btn.classList.remove('active');
                } else {
                    // If not active, activate it (and optionally deactivate others)
                    // For exclusive selection, uncomment the next line:
                    // featureButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                }

                // Handle different button actions
                const buttonId = btn.id;
                this.handleFeatureButtonClick(buttonId, !isActive);
            });
        });
    }

    handleFeatureButtonClick(buttonId, isActivated) {
        const buttonActions = {
            'deepThinkBtn': (activated) => {
                const message = activated ? '已启用深度思考模式 (R1)' : '已关闭深度思考模式';
                this.showNotification(message, 'success');
            },
            'webSearchBtn': (activated) => {
                const message = activated ? '已启用联网搜索功能' : '已关闭联网搜索功能';
                this.showNotification(message, 'success');
            }
        };

        const action = buttonActions[buttonId];
        if (action) {
            action(isActivated);
        }
    }

    getSuggestionText(card) {
        const suggestions = {
            '创意写作': '请帮我写一个关于未来科技的短故事',
            '编程助手': '请帮我解释一下JavaScript中的闭包概念',
            '学习辅导': '请为我制定一个学习计划',
            '数据分析': '请帮我分析这组数据的趋势'
        };
        
        const cardText = card.querySelector('span').textContent;
        return suggestions[cardText] || `关于${cardText}的问题`;
    }

    selectChatItem(item) {
        // Remove active class from all items
        document.querySelectorAll('.chat-item').forEach(i => {
            i.classList.remove('active');
        });
        
        // Add active class to clicked item
        item.classList.add('active');
        
        // Load chat (in a real app, this would load the actual chat history)
        this.loadChatHistory(item.querySelector('span').textContent);
    }

    loadChatHistory(chatTitle) {
        // Hide welcome section and show a sample conversation
        const welcomeSection = document.querySelector('.welcome-section');
        const messagesContainer = document.getElementById('messages');
        
        welcomeSection.style.display = 'none';
        
        // Clear existing messages
        messagesContainer.innerHTML = '';
        
        // Add sample messages based on chat title
        this.addSampleMessages(chatTitle, messagesContainer);
    }

    addSampleMessages(chatTitle, container) {
        const sampleConversations = {
            '我是 DeepSeek，很高兴见到你！': [
                { type: 'user', content: '你好！' },
                { type: 'assistant', content: '你好！我是 DeepSeek，很高兴见到你！我可以帮助你解决问题，回答疑问，或者进行有趣的对话。有什么我可以为你做的吗？' }
            ],
            'Vue组件实现动态效果': [
                { type: 'user', content: '如何在Vue组件中实现动态效果？' },
                { type: 'assistant', content: '在Vue中实现动态效果有多种方法：\n\n1. **CSS过渡和动画**\n2. **Vue的内置过渡组件**\n3. **第三方动画库**\n\n你想了解哪种方法呢？' }
            ]
        };
        
        const messages = sampleConversations[chatTitle] || [
            { type: 'user', content: chatTitle },
            { type: 'assistant', content: '这是一个很好的问题！让我来帮你解答。' }
        ];
        
        messages.forEach(msg => {
            this.addMessageToDOM(msg.type, msg.content, container);
        });
    }

    startNewChat() {
        // Clear messages and show welcome section
        const welcomeSection = document.querySelector('.welcome-section');
        const messagesContainer = document.getElementById('messages');
        
        welcomeSection.style.display = 'block';
        messagesContainer.innerHTML = '';
        
        // Clear input
        const messageInput = document.getElementById('messageInput');
        messageInput.value = '';
        messageInput.style.height = 'auto';
        
        // Reset active chat item
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Reset messages array
        this.messages = [];
    }

    async sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();
        
        if (!message || this.isTyping) return;
        
        // Hide welcome section if visible
        const welcomeSection = document.querySelector('.welcome-section');
        welcomeSection.style.display = 'none';
        
        // Add user message
        this.addMessage('user', message);
        
        // Clear input
        messageInput.value = '';
        messageInput.style.height = 'auto';
        
        // Show typing indicator
        this.showTypingIndicator();
        
        // Simulate AI response
        setTimeout(() => {
            this.hideTypingIndicator();
            this.addMessage('assistant', this.generateResponse(message));
        }, 1000 + Math.random() * 2000);
    }

    addMessage(type, content) {
        const message = { type, content, timestamp: new Date() };
        this.messages.push(message);
        
        const messagesContainer = document.getElementById('messages');
        this.addMessageToDOM(type, content, messagesContainer);
        
        // Scroll to bottom
        const chatContainer = document.getElementById('chatContainer');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    addMessageToDOM(type, content, container) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-brain"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';
        messageBubble.textContent = content;
        
        messageContent.appendChild(messageBubble);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        container.appendChild(messageDiv);
    }

    showTypingIndicator() {
        this.isTyping = true;
        const messagesContainer = document.getElementById('messages');
        
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant typing';
        typingDiv.id = 'typing-indicator';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<i class="fas fa-brain"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
        
        messageContent.appendChild(typingIndicator);
        typingDiv.appendChild(avatar);
        typingDiv.appendChild(messageContent);
        
        messagesContainer.appendChild(typingDiv);
        
        // Scroll to bottom
        const chatContainer = document.getElementById('chatContainer');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    generateResponse(userMessage) {
        const responses = [
            '这是一个很有趣的问题！让我来为你详细解答。',
            '根据你的问题，我建议从以下几个方面来考虑...',
            '我理解你的需求。这里有一些建议可能对你有帮助。',
            '这确实是一个值得深入探讨的话题。',
            '让我来帮你分析一下这个问题的关键点。',
            '基于我的理解，我认为可以这样来解决这个问题。'
        ];
        
        // Simple keyword-based responses
        if (userMessage.includes('代码') || userMessage.includes('编程')) {
            return '关于编程问题，我建议你先明确需求，然后选择合适的技术栈。如果你能提供更具体的信息，我可以给出更详细的建议。';
        }
        
        if (userMessage.includes('学习') || userMessage.includes('教程')) {
            return '学习新技能需要循序渐进。我建议制定一个清晰的学习计划，结合理论学习和实践练习。你想学习什么具体的内容呢？';
        }
        
        if (userMessage.includes('如何') || userMessage.includes('怎么')) {
            return '这是一个很实用的问题。通常解决这类问题需要分步骤进行。你可以先尝试基础方法，如果遇到困难，我们可以探讨更高级的解决方案。';
        }
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
}

// Initialize the chat application
document.addEventListener('DOMContentLoaded', () => {
    new DeepSeekChat();
});

// Handle window resize for responsive design
window.addEventListener('resize', () => {
    const sidebar = document.getElementById('sidebar');
    if (window.innerWidth > 768) {
        sidebar.classList.remove('open');
    }
});
